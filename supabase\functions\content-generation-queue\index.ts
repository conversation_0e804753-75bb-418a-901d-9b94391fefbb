import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface QueueRequest {
  summary_id: string;
  target_platforms: string[];
  style: string;
  user_input?: string;
  priority?: number;
  user_id: string; // 现在是必需参数
  user_language?: string; // 用户语言偏好 ('en' | 'zh')
}

interface QueueResponse {
  success: boolean;
  task_id?: string;
  estimated_wait_time?: number;
  queue_position?: number;
  error?: string;
}

// 队列配置
const QUEUE_CONFIG = {
  MAX_CONCURRENT_TASKS: 10,
  MAX_QUEUE_SIZE: 1000,
  DEFAULT_PRIORITY: 0
};





Deno.serve(async (req) => {
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[QUEUE-MANAGER] Request received, using correct service role key');
    
    // 验证请求方法
    if (req.method !== 'POST') {
      throw new Error('Only POST method is allowed')
    }

    // 解析请求体
    const requestBody: QueueRequest = await req.json()
    const { summary_id, target_platforms, style, user_input, priority = QUEUE_CONFIG.DEFAULT_PRIORITY, user_id, user_language = 'zh' } = requestBody

    // 验证必需参数
    if (!summary_id || !target_platforms || !Array.isArray(target_platforms) || target_platforms.length === 0 || !style || !user_id) {
      throw new Error('Missing required parameters: summary_id, target_platforms, style, user_id')
    }

    // 验证语言参数
    if (user_language && !['en', 'zh'].includes(user_language)) {
      throw new Error('Invalid user_language: must be "en" or "zh"')
    }

    // 验证平台参数
    const validPlatforms = ['linkedin', 'twitter', 'reddit', 'xiaohongshu', 'wechat']
    const invalidPlatforms = target_platforms.filter(p => !validPlatforms.includes(p))
    if (invalidPlatforms.length > 0) {
      throw new Error(`Invalid platforms: ${invalidPlatforms.join(', ')}`)
    }

    // 验证风格参数
    const validStyles = ['engaging', 'professional', 'casual', 'creative', 'analytical']
    if (!validStyles.includes(style)) {
      throw new Error(`Invalid style: ${style}`)
    }

    console.log(`[QUEUE-MANAGER] Request: summary_id=${summary_id}, platforms=${target_platforms.join(',')}, style=${style}, priority=${priority}, user_language=${user_language}`)

    // 创建Supabase客户端
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // 使用传入的user_id
    const userId = user_id;
    console.log(`[QUEUE-MANAGER] Using user ID: ${userId}, language: ${user_language}`);

    // 检查队列大小
    const { count: queueSize, error: countError } = await supabaseClient
      .from('content_generation_queue')
      .select('*', { count: 'exact', head: true })
      .in('status', ['pending', 'processing'])

    if (countError) {
      console.error('[QUEUE-MANAGER] Error checking queue size:', countError)
      throw new Error('Failed to check queue status')
    }

    if (queueSize && queueSize >= QUEUE_CONFIG.MAX_QUEUE_SIZE) {
      throw new Error('Queue is full, please try again later')
    }

    // 验证摘要是否存在
    const { data: summaryData, error: summaryError } = await supabaseClient
      .from('summaries')
      .select('id')
      .eq('id', summary_id)
      .single()

    if (summaryError || !summaryData) {
      throw new Error('Summary not found')
    }

    // 创建队列任务
    const { data: queueTask, error: insertError } = await supabaseClient
      .from('content_generation_queue')
      .insert({
        user_id: userId,
        summary_id: summary_id,
        target_platforms: target_platforms,
        style: style,
        user_input: user_input || null,
        priority: priority,
        status: 'pending',
        user_language: user_language
      })
      .select()
      .single()

    if (insertError) {
      console.error('[QUEUE-MANAGER] Failed to create queue task:', insertError)
      throw new Error(`Failed to create queue task: ${insertError.message}`)
    }

    // 计算队列位置和预估等待时间
    const { count: queuePosition, error: positionError } = await supabaseClient
      .from('content_generation_queue')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending')
      .or(`priority.gt.${priority},and(priority.eq.${priority},created_at.lt.${queueTask.created_at})`)

    if (positionError) {
      console.warn('[QUEUE-MANAGER] Failed to calculate queue position:', positionError)
    }

    // 预估等待时间（假设每个任务平均需要2分钟）
    const estimatedWaitTime = Math.max(0, (queuePosition || 0) * 2)

    const response: QueueResponse = {
      success: true,
      task_id: queueTask.id,
      queue_position: (queuePosition || 0) + 1,
      estimated_wait_time: estimatedWaitTime
    }

    console.log(`[QUEUE-MANAGER] Task created successfully: ${queueTask.id}, position: ${response.queue_position}, wait: ${estimatedWaitTime}min`)

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error: any) {
    console.error('[QUEUE-MANAGER] Error:', error.message)
    
    let statusCode = 400
    if (error.message.includes('not found')) {
      statusCode = 404
    } else if (error.message.includes('Queue is full')) {
      statusCode = 503
    }

    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      { 
        status: statusCode,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})
