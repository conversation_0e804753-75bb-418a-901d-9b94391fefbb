# 使用官方Node.js镜像
FROM node:18-alpine

# 安装ffmpeg和其他必要工具 (包含MP3编码器)
RUN apk add --no-cache ffmpeg lame

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --omit=dev

# 复制应用代码
COPY src/ ./src/

# 创建临时文件目录
RUN mkdir -p /tmp/audio-processing

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:8080/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
CMD ["npm", "start"]
