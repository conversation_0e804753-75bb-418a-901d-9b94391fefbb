import { Link } from 'react-router-dom';
import { Settings, Sparkles, User, LogOut, Database, Wand2, Shield, History, Globe, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@/components/ui/sheet';

const Navbar = () => {
  const { user, profile, isAdmin, isAuthenticated, signOut } = useAuth();
  const { toast } = useToast();
  const { language, changeLanguage } = useLanguage();
  const { t } = useTranslation();
  const isMobile = useIsMobile();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleSignOut = async () => {
    console.log('handleSignOut called');
    try {
      console.log('Calling signOut...');
      await signOut();
      console.log('signOut completed successfully');
      toast({
        title: t('auth.signOutSuccess'),
        description: t('auth.signOutSuccessDesc'),
      });
    } catch (error: any) {
      console.error('signOut error:', error);
      toast({
        title: t('auth.signOutError'),
        description: error.message || t('auth.signOutErrorDesc'),
        variant: 'destructive',
      });
    }
  };

  return (
    <nav className="glass-effect border-b border-border/50">
      <div className="max-w-7xl mx-auto mobile-navbar px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 group magnetic">
            <div className="p-2 bg-gradient-primary rounded-lg shadow-glow group-hover:shadow-elegant group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 transform-gpu">
              <Sparkles className="h-6 w-6 text-primary-foreground group-hover:animate-spin" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-gradient group-hover:scale-105 transition-transform duration-300">
                {t('nav.title')}
              </h1>
              <p className="text-xs text-muted-foreground group-hover:text-primary transition-colors duration-300">{t('nav.subtitle')}</p>
            </div>
            <div className="sm:hidden">
              <h1 className="text-lg font-bold text-gradient group-hover:scale-105 transition-transform duration-300">
                {t('nav.title')}
              </h1>
            </div>
          </Link>



          {/* Desktop Navigation Links */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Language Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground magnetic hover:scale-105 transition-all duration-300">
                  <Globe className="h-4 w-4 mr-2" />
                  {language === 'zh' ? '中文' : 'English'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => changeLanguage('en')}>
                  English
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => changeLanguage('zh')}>
                  中文
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            {isAuthenticated ? (
              <>
                <Link to="/profile">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground glass-effect px-3 py-1 rounded-lg hover:bg-primary/10 transition-colors cursor-pointer">
                    <User className="h-4 w-4 text-primary" />
                    <span className="hover:text-primary transition-colors duration-300">
                      {profile?.display_name || user?.email}
                    </span>
                    {isAdmin && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        <Shield className="h-3 w-3 mr-1" />
                        {t('auth.adminBadge')}
                      </Badge>
                    )}
                  </div>
                </Link>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSignOut}
                  className="text-muted-foreground hover:text-foreground magnetic hover:bg-destructive/10 hover:text-destructive transition-all duration-300"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  {t('nav.logout')}
                </Button>
              </>
            ) : (
              <Link to={language === 'zh' ? '/zh/auth' : '/auth'}>
                <button className="modern-button pulse-button group flex items-center text-sm">
                  <User className="h-4 w-4 mr-2 group-hover:animate-pulse" />
                  {t('nav.login')}
                </button>
              </Link>
            )}

            {isAuthenticated && (
              <Link to={language === 'zh' ? '/zh/content-summary' : '/content-summary'}>
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground magnetic hover:scale-105 transition-all duration-300">
                  <Database className="h-4 w-4 mr-2 hover:animate-pulse transition-transform duration-300" />
                  {t('nav.dailySummary')}
                </Button>
              </Link>
            )}

            {isAuthenticated && (
              <Link to={language === 'zh' ? '/zh/content-history' : '/content-history'}>
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground magnetic hover:scale-105 transition-all duration-300">
                  <History className="h-4 w-4 mr-2 hover:animate-pulse transition-transform duration-300" />
                  {t('nav.socialContent')}
                </Button>
              </Link>
            )}

            {isAdmin && (
              <Link to={language === 'zh' ? '/zh/admin' : '/admin'}>
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground magnetic hover:scale-105 transition-all duration-300">
                  <Settings className="h-4 w-4 mr-2 hover:animate-spin transition-transform duration-300" />
                  {t('nav.admin')}
                </Button>
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                <div className="flex flex-col space-y-4 mt-6">
                  {/* Language Selector */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('nav.language')}</span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Globe className="h-4 w-4 mr-2" />
                          {language === 'zh' ? '中文' : 'English'}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => changeLanguage('en')}>
                          English
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => changeLanguage('zh')}>
                          中文
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* User Info */}
                  {isAuthenticated ? (
                    <div className="border-t pt-4">
                      <Link to="/profile" onClick={() => setMobileMenuOpen(false)}>
                        <div className="flex items-center space-x-3 p-3 rounded-lg hover:bg-muted transition-colors">
                          <User className="h-5 w-5 text-primary" />
                          <div className="flex-1">
                            <div className="text-sm font-medium">
                              {profile?.display_name || user?.email}
                            </div>
                            {isAdmin && (
                              <Badge variant="secondary" className="mt-1 text-xs">
                                <Shield className="h-3 w-3 mr-1" />
                                {t('auth.adminBadge')}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </Link>
                    </div>
                  ) : (
                    <div className="border-t pt-4">
                      <Link to={language === 'zh' ? '/zh/auth' : '/auth'} onClick={() => setMobileMenuOpen(false)}>
                        <Button className="w-full" size="lg">
                          <User className="h-4 w-4 mr-2" />
                          {t('nav.login')}
                        </Button>
                      </Link>
                    </div>
                  )}

                  {/* Navigation Links */}
                  {isAuthenticated && (
                    <div className="space-y-2">
                      <Link to={language === 'zh' ? '/zh/content-summary' : '/content-summary'} onClick={() => setMobileMenuOpen(false)}>
                        <Button variant="ghost" className="w-full justify-start" size="lg">
                          <Database className="h-4 w-4 mr-3" />
                          {t('nav.dailySummary')}
                        </Button>
                      </Link>

                      <Link to={language === 'zh' ? '/zh/content-history' : '/content-history'} onClick={() => setMobileMenuOpen(false)}>
                        <Button variant="ghost" className="w-full justify-start" size="lg">
                          <History className="h-4 w-4 mr-3" />
                          {t('nav.socialContent')}
                        </Button>
                      </Link>

                      {isAdmin && (
                        <Link to={language === 'zh' ? '/zh/admin' : '/admin'} onClick={() => setMobileMenuOpen(false)}>
                          <Button variant="ghost" className="w-full justify-start" size="lg">
                            <Settings className="h-4 w-4 mr-3" />
                            {t('nav.admin')}
                          </Button>
                        </Link>
                      )}

                      <Button
                        variant="ghost"
                        className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
                        size="lg"
                        onClick={() => {
                          handleSignOut();
                          setMobileMenuOpen(false);
                        }}
                      >
                        <LogOut className="h-4 w-4 mr-3" />
                        {t('nav.logout')}
                      </Button>
                    </div>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;