import { TTSProvider, TTSRequest, TTSResponse, TTSConfig, SpeakerType } from './types.ts';

export class KokoroTTSProvider extends TTSProvider {
  private apiUrl: string;
  private apiToken: string;
  private modelVersion: string;

  constructor(config: { apiUrl?: string; apiToken?: string; modelVersion?: string } = {}) {
    super('Kokoro TTS', config);
    this.apiUrl = config.apiUrl || 'https://api.replicate.com/v1/predictions';
    this.apiToken = config.apiToken || Deno.env.get('REPLICATE_API_TOKEN') || '';
    this.modelVersion = config.modelVersion || 'jaaari/kokoro-82m:9b835af53a2383159eaf0147c9bee26bf238bdfa7527eb2e9c24bb4b43dac02d';
  }

  getSupportedSpeakers(): SpeakerType[] {
    return ['xiaoli', 'xiaowang', 'joy', 'sam'];
  }

  getSpeakerConfig(speaker: SpeakerType): TTSConfig {
    const voiceConfig = {
      xiaoli: { voice: 'af_bella', speed: 1.5 }, // Chinese female voice -> English female (best quality)
      xiaowang: { voice: 'am_michael', speed: 1.5 }, // Chinese male voice -> English male
      joy: { voice: 'af_bella', speed: 1.25 }, // English female voice (best quality)
      sam: { voice: 'am_michael', speed: 1.25 } // English male voice
    };

    return voiceConfig[speaker];
  }

  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    const { text, speaker } = request;

    if (!this.isSpeakerSupported(speaker)) {
      throw new Error(`Speaker ${speaker} is not supported by ${this.getName()}`);
    }

    if (!this.apiToken) {
      throw new Error('REPLICATE_API_TOKEN is required for Kokoro TTS provider');
    }

    const config = this.getSpeakerConfig(speaker);
    const cleanedText = this.cleanTextForTTS(text);

    console.log(`🔊 Calling ${this.getName()} for ${speaker} (${config.voice}): ${cleanedText.length} characters`);

    // Prepare the request body for Replicate API
    const requestBody = {
      version: this.modelVersion,
      input: {
        text: cleanedText,
        voice: config.voice,
        speed: config.speed
      }
    };

    console.log(`📤 Sending to Replicate API:`);
    console.log(`   Full JSON body: ${JSON.stringify(requestBody, null, 2)}`);

    // Call Replicate API without wait header first (async mode)
    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log(`📥 Replicate API Response:`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Error response body: ${errorText}`);
      throw new Error(`Replicate API error: ${response.status} - ${errorText}`);
    }

    let result = await response.json();
    console.log(`📥 Replicate prediction result:`, result);

    // If not immediately succeeded, poll for completion with timeout
    if (result.status !== 'succeeded') {
      console.log(`⏳ Prediction status: ${result.status}, polling for completion...`);

      const maxWaitTime = 90; // Maximum 90 seconds total wait
      const pollInterval = 2; // Poll every 2 seconds
      const maxPolls = Math.floor(maxWaitTime / pollInterval);
      let pollCount = 0;

      while (result.status !== 'succeeded' && result.status !== 'failed' && pollCount < maxPolls) {
        await new Promise(resolve => setTimeout(resolve, pollInterval * 1000));

        const pollResponse = await fetch(result.urls.get, {
          headers: {
            'Authorization': `Bearer ${this.apiToken}`,
          }
        });

        if (pollResponse.ok) {
          result = await pollResponse.json();
          console.log(`📊 Poll ${pollCount + 1}/${maxPolls}: Status = ${result.status}`);
        }

        pollCount++;
      }

      // If still not completed after max wait time, use delayed retry
      if (result.status !== 'succeeded' && result.status !== 'failed') {
        console.log(`⏰ Prediction still ${result.status} after ${maxWaitTime}s, scheduling delayed retry`);
        const delayedRetryError = new Error(`REPLICATE_TIMEOUT: Prediction timed out after ${maxWaitTime}s (status: ${result.status}), will retry in 3 minutes`);
        (delayedRetryError as any).isDelayedRetry = true;
        (delayedRetryError as any).retryDelayMinutes = 3;
        throw delayedRetryError;
      }
    }

    // Check final result
    if (result.status === 'failed') {
      throw new Error(`Replicate prediction failed: ${result.error}`);
    }

    if (result.status !== 'succeeded' || !result.output) {
      throw new Error(`Replicate prediction did not succeed. Status: ${result.status}`);
    }

    // The output should be a URL to the audio file
    const audioUrl = result.output;
    if (!audioUrl) {
      throw new Error('No audio URL returned from Replicate');
    }

    console.log(`🔗 Downloading audio from: ${audioUrl}`);

    // Download the audio file
    const audioResponse = await fetch(audioUrl);
    if (!audioResponse.ok) {
      throw new Error(`Failed to download audio: ${audioResponse.status} - ${audioResponse.statusText}`);
    }

    const audioBuffer = await audioResponse.arrayBuffer();
    console.log(`✅ Kokoro TTS completed for ${speaker}, received ${audioBuffer.byteLength} bytes`);

    // Verify we got actual audio data by checking the first few bytes
    const firstBytes = new Uint8Array(audioBuffer.slice(0, 10));
    console.log(`🔍 First 10 bytes of audio data: ${Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);

    // Check if it looks like MP3 data (should start with ID3 tag or MP3 frame sync)
    const isMP3 = firstBytes[0] === 0x49 && firstBytes[1] === 0x44 && firstBytes[2] === 0x33; // ID3
    const isMP3Frame = firstBytes[0] === 0xFF && (firstBytes[1] & 0xE0) === 0xE0; // MP3 frame sync
    console.log(`🎵 Audio format check: ID3=${isMP3}, MP3Frame=${isMP3Frame}`);

    return {
      audioBuffer,
      metadata: {
        format: 'mp3',
        size: audioBuffer.byteLength
      }
    };
  }
}
