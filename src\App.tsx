import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { LanguageProvider } from "./contexts/LanguageContext";
import { Analytics } from "@vercel/analytics/react";
import Index from "./pages/Index";
import TopicDetailNew from "./pages/TopicDetailNew";
import Admin from "./pages/Admin";
import Auth from "./pages/Auth";
import NotFound from "./pages/NotFound";
import DataTest from "./pages/DataTest";
import SimpleTest from "./pages/SimpleTest";
import LanguageFilterTest from "./pages/LanguageFilterTest";
import DatabaseTest from "./pages/DatabaseTest";
import ContentGenerator from "./pages/ContentGenerator";
import ContentSummaryLeftRight from "./pages/ContentSummaryLeftRight";
import UserContentHistoryPage from "./pages/UserContentHistoryPage";
import Profile from "./pages/Profile";
import AuthTest from "./pages/AuthTest";
import AuthCallback from "./pages/AuthCallback";
import Unsubscribe from "./pages/Unsubscribe";
import ProtectedRoute from "./components/ProtectedRoute";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <Analytics />
        <BrowserRouter>
          <LanguageProvider>
            <Routes>
              {/* English routes (default) */}
              <Route path="/" element={<Index />} />
              <Route path="/topics/:id" element={<TopicDetailNew />} />
              <Route path="/admin" element={
                <ProtectedRoute requiredRole="admin">
                  <Admin />
                </ProtectedRoute>
              } />
              <Route path="/content-generator" element={
                <ProtectedRoute>
                  <ContentGenerator />
                </ProtectedRoute>
              } />
              <Route path="/content-summary" element={
                <ProtectedRoute>
                  <ContentSummaryLeftRight />
                </ProtectedRoute>
              } />
              <Route path="/content-history" element={
                <ProtectedRoute>
                  <UserContentHistoryPage />
                </ProtectedRoute>
              } />
              <Route path="/profile" element={
                <ProtectedRoute>
                  <Profile />
                </ProtectedRoute>
              } />
              <Route path="/auth" element={<Auth />} />
              <Route path="/auth/callback" element={<AuthCallback />} />
              <Route path="/unsubscribe" element={<Unsubscribe />} />
              <Route path="/auth-test" element={<AuthTest />} />
              <Route path="/data-test" element={<DataTest />} />
              <Route path="/simple-test" element={<SimpleTest />} />
              <Route path="/language-filter-test" element={<LanguageFilterTest />} />
              <Route path="/database-test" element={<DatabaseTest />} />

              {/* Chinese routes (/zh prefix) */}
              <Route path="/zh" element={<Index />} />
              <Route path="/zh/topics/:id" element={<TopicDetailNew />} />
              <Route path="/zh/admin" element={
                <ProtectedRoute requiredRole="admin">
                  <Admin />
                </ProtectedRoute>
              } />
              <Route path="/zh/content-generator" element={
                <ProtectedRoute>
                  <ContentGenerator />
                </ProtectedRoute>
              } />
              <Route path="/zh/content-summary" element={
                <ProtectedRoute>
                  <ContentSummaryLeftRight />
                </ProtectedRoute>
              } />
              <Route path="/zh/content-history" element={
                <ProtectedRoute>
                  <UserContentHistoryPage />
                </ProtectedRoute>
              } />
              <Route path="/zh/profile" element={
                <ProtectedRoute>
                  <Profile />
                </ProtectedRoute>
              } />
              <Route path="/zh/auth" element={<Auth />} />
              <Route path="/zh/auth/callback" element={<AuthCallback />} />
              <Route path="/zh/unsubscribe" element={<Unsubscribe />} />
              <Route path="/zh/auth-test" element={<AuthTest />} />
              <Route path="/zh/data-test" element={<DataTest />} />
              <Route path="/zh/simple-test" element={<SimpleTest />} />
              <Route path="/zh/language-filter-test" element={<LanguageFilterTest />} />
              <Route path="/zh/database-test" element={<DatabaseTest />} />

              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </LanguageProvider>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
