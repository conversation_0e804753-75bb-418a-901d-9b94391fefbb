import { ArrowR<PERSON>, TrendingUp, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getTodayInUTC } from '@/lib/timezone-utils';
import { useTimezone } from '@/hooks/useTimezone';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';

interface TopicCardProps {
  id: string;
  name: string;
  description: string;
  contentCount: number;
  platforms: string[];
  isPopular?: boolean;
  gradient?: string;
}

const TopicCard = ({
  id,
  name,
  description,
  contentCount,
  platforms,
  isPopular = false,
  gradient = "from-primary to-primary-glow"
}: TopicCardProps) => {
  const { timezone } = useTimezone();
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Generate today's date parameters (single day)
  const today = getTodayInUTC(timezone);
  const dateParams = new URLSearchParams({
    topic: id,
    date: today.toISOString(),
    timezone: timezone
  });

  const linkPath = language === 'zh' ? '/zh/content-summary' : '/content-summary';

  return (
    <Link to={`${linkPath}?${dateParams.toString()}`} className="block h-full">
      <Card className="group relative overflow-hidden border-0 h-full interactive-card hover-card magnetic gradient-border cursor-pointer">
        {/* Shimmer Effect */}
        <div className="absolute inset-0 shimmer opacity-0 group-hover:opacity-20 transition-opacity duration-300" />
        
        <CardHeader className="pb-3 relative">
          <div className="flex items-start justify-between">
            <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${gradient} flex items-center justify-center mb-3 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 transform-gpu`}>
              <span className="text-white text-lg font-bold group-hover:animate-pulse">
                {name.charAt(0)}
              </span>
            </div>
            {isPopular && (
              <div className="pulse-button">
                <Badge variant="secondary" className="bg-primary/10 text-primary border-0 group-hover:bg-primary/20 transition-all duration-300">
                  <Sparkles className="h-3 w-3 mr-1 group-hover:animate-spin" />
                  {language === 'zh' ? '热门' : 'Popular'}
                </Badge>
              </div>
            )}
          </div>
          
          <div className="space-y-1">
            <h3 className="text-xl font-bold group-hover:text-gradient transition-all duration-300 transform-gpu group-hover:scale-[1.02]">
              {name}
            </h3>
            <p className="text-sm text-muted-foreground leading-relaxed group-hover:text-foreground/70 transition-colors duration-300">
              {description}
            </p>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0 relative">
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground group-hover:text-primary transition-colors duration-300">{t('home.topics.todaySummaries')}</span>
              <span className="font-medium text-primary group-hover:text-primary-glow transition-colors duration-300 group-hover:scale-110 transform-gpu">{contentCount} {language === 'zh' ? '条内容' : 'items'}</span>
            </div>
            
            <div className="flex flex-wrap gap-1">
              {platforms.slice(0, 4).map((platform, index) => (
                <Badge 
                  key={platform} 
                  variant="outline" 
                  className="text-xs group-hover:bg-primary/10 group-hover:border-primary/30 group-hover:text-primary transition-all duration-300 transform-gpu hover:scale-105"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {platform}
                </Badge>
              ))}
              {platforms.length > 4 && (
                <Badge variant="outline" className="text-xs group-hover:bg-primary/10 group-hover:border-primary/30 group-hover:text-primary transition-all duration-300">
                  +{platforms.length - 4}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center justify-end pt-2">
              <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-2 group-hover:scale-110 transition-all duration-300 transform-gpu" />
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default TopicCard;