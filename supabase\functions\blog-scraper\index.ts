import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface BlogScrapingRequest {
  task_ids: string[];
  tasks: Array<{
    id: string;
    platform: string;
    topic_id: string;
    datasource_id: string;
    target_date: string;
    metadata: any;
  }>;
}

interface BlogScrapingResponse {
  success: boolean;
  message: string;
  totalPostsScraped: number;
  tasksProcessed: number;
  taskResults: Array<{
    taskId: string;
    datasourceId: string;
    postsScraped: number;
    success: boolean;
    error?: string;
  }>;
}

interface BlogPost {
  title: string;
  content: string;
  author: string;
  url: string;
  external_id: string;
  published_at: string;
  metadata: any;
}

// Parse RSS feed and extract posts
async function parseRSSFeed(rssUrl: string, config: any): Promise<BlogPost[]> {
  console.log(`Fetching RSS feed: ${rssUrl}`);
  
  try {
    const response = await fetch(rssUrl, {
      headers: {
        'User-Agent': 'topic-stream-weaver/1.0 RSS Reader'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch RSS feed: ${response.status} ${response.statusText}`);
    }

    const rssText = await response.text();
    console.log(`RSS feed fetched, length: ${rssText.length} characters`);

    // Parse RSS XML
    const posts = await parseRSSXML(rssText, config);
    console.log(`Parsed ${posts.length} posts from RSS feed`);

    return posts;
  } catch (error) {
    console.error(`Error fetching RSS feed ${rssUrl}:`, error);
    throw error;
  }
}

// Parse RSS/Atom XML content
async function parseRSSXML(xmlContent: string, config: any): Promise<BlogPost[]> {
  const posts: BlogPost[] = [];

  try {
    // Detect feed format (RSS vs Atom)
    const isAtomFeed = xmlContent.includes('<feed') && xmlContent.includes('xmlns="http://www.w3.org/2005/Atom"');

    let items: string[] = [];
    if (isAtomFeed) {
      // Extract entries between <entry> and </entry> tags for Atom feeds
      const entryRegex = /<entry[^>]*>([\s\S]*?)<\/entry>/gi;
      items = xmlContent.match(entryRegex) || [];
      console.log(`Found ${items.length} entries in Atom feed`);
    } else {
      // Extract items between <item> and </item> tags for RSS feeds
      const itemRegex = /<item[^>]*>([\s\S]*?)<\/item>/gi;
      items = xmlContent.match(itemRegex) || [];
      console.log(`Found ${items.length} items in RSS feed`);
    }

    const maxPosts = config.max_posts_per_crawl || 10;
    const timeFilterHours = config.time_filter_hours || 24;
    const cutoffTime = new Date(Date.now() - (timeFilterHours * 60 * 60 * 1000));

    for (let i = 0; i < Math.min(items.length, maxPosts); i++) {
      const item = items[i];

      try {
        const post = isAtomFeed ? parseAtomEntry(item) : parseRSSItem(item);

        // Filter by time if specified
        if (timeFilterHours > 0) {
          const postDate = new Date(post.published_at);
          if (postDate < cutoffTime) {
            console.log(`Skipping old post: ${post.title} (${postDate.toISOString()})`);
            continue;
          }
        }

        posts.push(post);
      } catch (error) {
        console.error(`Error parsing ${isAtomFeed ? 'Atom entry' : 'RSS item'} ${i}:`, error);
        continue;
      }
    }

    console.log(`Parsed ${posts.length} valid posts after filtering`);
    return posts;
  } catch (error) {
    console.error('Error parsing RSS/Atom XML:', error);
    throw error;
  }
}

// Parse individual Atom entry
function parseAtomEntry(entryXml: string): BlogPost {
  const extractTag = (tagName: string): string => {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = entryXml.match(regex);
    return match ? match[1].trim() : '';
  };

  const extractAtomLink = (): string => {
    const linkRegex = /<link[^>]*href="([^"]*)"[^>]*>/i;
    const match = entryXml.match(linkRegex);
    return match ? match[1] : '';
  };

  const extractCDATA = (content: string): string => {
    const cdataRegex = /<!\[CDATA\[([\s\S]*?)\]\]>/;
    const match = content.match(cdataRegex);
    return match ? match[1] : content;
  };

  // Extract fields from Atom entry
  const title = extractCDATA(extractTag('title'));
  const content = extractCDATA(extractTag('content'));
  const summary = extractCDATA(extractTag('summary'));
  const link = extractAtomLink();
  const updated = extractTag('updated');
  const id = extractTag('id');
  const author = extractTag('author') || extractTag('name'); // Author might be nested in <author><name>

  // Use content if available, otherwise use summary
  const postContent = content || summary || '';

  // Parse publication date (Atom uses <updated>)
  let publishedAt: string;
  try {
    publishedAt = new Date(updated).toISOString();
  } catch (error) {
    console.warn(`Invalid date format: ${updated}, using current time`);
    publishedAt = new Date().toISOString();
  }

  // Clean HTML tags from content
  const cleanContent = postContent
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Generate external_id from Atom id or link
  const externalId = id || link || `atom-${Date.now()}`;

  return {
    title: title || 'Untitled',
    content: cleanContent,
    author: author,
    url: link,
    external_id: externalId,
    published_at: publishedAt,
    metadata: {
      original_content: content,
      original_summary: summary,
      updated: updated,
      atom_id: id,
      content_length: cleanContent.length
    }
  };
}

// Parse individual RSS item
function parseRSSItem(itemXml: string): BlogPost {
  const extractTag = (tagName: string): string => {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = itemXml.match(regex);
    return match ? match[1].trim() : '';
  };

  const extractCDATA = (content: string): string => {
    const cdataRegex = /<!\[CDATA\[([\s\S]*?)\]\]>/;
    const match = content.match(cdataRegex);
    return match ? match[1] : content;
  };

  const title = extractCDATA(extractTag('title'));
  const description = extractCDATA(extractTag('description'));
  const content = extractCDATA(extractTag('content:encoded')) || description;
  const link = extractTag('link');
  const guid = extractTag('guid');
  const author = extractTag('dc:creator') || extractTag('author') || 'Unknown';
  const pubDate = extractTag('pubDate') || extractTag('dc:date') || new Date().toISOString();

  // Generate unique external_id (priority: guid > link > fallback)
  let externalId = guid || link;
  if (!externalId) {
    // Use title + timestamp as fallback
    const titleHash = btoa(title + pubDate).replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
    externalId = `blog_${titleHash}_${Date.now()}`;
  }

  // Parse publication date
  let publishedAt: string;
  try {
    publishedAt = new Date(pubDate).toISOString();
  } catch (error) {
    console.warn(`Invalid date format: ${pubDate}, using current time`);
    publishedAt = new Date().toISOString();
  }

  // Clean HTML tags from content
  const cleanContent = content
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  return {
    title: title || 'Untitled',
    content: cleanContent,
    author: author,
    url: link,
    external_id: externalId,
    published_at: publishedAt,
    metadata: {
      original_description: description,
      pub_date: pubDate,
      rss_guid: guid,
      content_length: cleanContent.length
    }
  };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    const requestData: BlogScrapingRequest = await req.json()
    console.log('Blog Scraper: Received request:', JSON.stringify(requestData, null, 2))

    const { task_ids, tasks } = requestData
    let totalPostsScraped = 0

    // Process each task in parallel
    const taskProcessingPromises = tasks.map(async (task) => {
      console.log(`Blog Scraper: Processing task ${task.id} for datasource ${task.datasource_id}`)

      try {
        // Get datasource details
        const { data: datasource, error: datasourceError } = await supabaseClient
          .from('datasources')
          .select('source_url, source_name, config')
          .eq('id', task.datasource_id)
          .single()

        if (datasourceError || !datasource) {
          throw new Error(`Failed to fetch datasource: ${datasourceError?.message || 'Not found'}`)
        }

        console.log(`Blog Scraper: Scraping RSS feed: ${datasource.source_url}`)

        // Scrape RSS feed
        const posts = await parseRSSFeed(datasource.source_url, datasource.config || {})

        if (posts.length === 0) {
          console.log(`Blog Scraper: No posts found for ${datasource.source_name}`)

          // Update task status
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true
          }
        }

        // URL-level deduplication: Check if any of these post URLs have already been scraped
        const postUrls = posts.map(post => post.url).filter(url => url);
        let postsToProcess = posts;

        if (postUrls.length > 0) {
          const { data: existingPosts, error: urlCheckError } = await supabaseClient
            .from('posts')
            .select('id, url, external_id')
            .in('url', postUrls)
            .gte('created_at', new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString()) // Check last 7 days

          if (urlCheckError) {
            console.warn(`Warning: Failed to check URL duplicates: ${urlCheckError.message}`)
          } else if (existingPosts && existingPosts.length > 0) {
            // Check for URL overlaps
            const existingUrls = new Set(existingPosts.map(post => post.url));

            // Filter out posts that have already been scraped by URL
            const newPosts = posts.filter(post => !existingUrls.has(post.url));
            const duplicateUrlCount = posts.length - newPosts.length;

            if (duplicateUrlCount > 0) {
              console.log(`Blog Scraper: Found ${duplicateUrlCount} duplicate URLs, processing ${newPosts.length} new posts for ${datasource.source_name}`);
            }

            postsToProcess = newPosts;
          }
        }

        console.log(`Blog Scraper: Processing ${postsToProcess.length} posts (${posts.length - postsToProcess.length} duplicates filtered) for ${datasource.source_name}`)

        if (postsToProcess.length === 0) {
          console.log(`Blog Scraper: All posts for ${datasource.source_name} have already been scraped, skipping`)

          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true,
            message: 'All posts already scraped'
          }
        }

        // Save posts to database
        const postsToInsert = postsToProcess.map(post => ({
          datasource_id: task.datasource_id,
          external_id: post.external_id,
          title: post.title,
          content: post.content,
          author: post.author,
          url: post.url,
          published_at: post.published_at,
          metadata: post.metadata
        }))

        const { data: insertedPosts, error: insertError } = await supabaseClient
          .from('posts')
          .upsert(postsToInsert, {
            onConflict: 'datasource_id,external_id',
            ignoreDuplicates: true
          })
          .select()

        if (insertError) {
          throw new Error(`Failed to insert posts: ${insertError.message}`)
        }

        const actualPostsInserted = insertedPosts?.length || 0

        console.log(`Blog Scraper: Inserted ${actualPostsInserted} posts for ${datasource.source_name}`)

        // Update task status
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'complete',
            posts_scraped: actualPostsInserted,
            completed_at: new Date().toISOString(),
            error_message: null
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: actualPostsInserted,
          success: true
        }

      } catch (error) {
        console.error(`Blog Scraper: Error processing task ${task.id}:`, error)

        // Update task status with error
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'failed',
            error_message: error.message,
            completed_at: new Date().toISOString()
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: 0,
          success: false,
          error: error.message
        }
      }
    })

    // Wait for all tasks to complete
    const taskResults = await Promise.all(taskProcessingPromises)
    totalPostsScraped = taskResults.reduce((sum, result) => sum + result.postsScraped, 0)

    console.log(`Blog Scraper: Completed processing ${tasks.length} tasks, total posts scraped: ${totalPostsScraped}`)

    const response: BlogScrapingResponse = {
      success: true,
      message: `Successfully processed ${tasks.length} blog scraping tasks`,
      totalPostsScraped,
      tasksProcessed: tasks.length,
      taskResults
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Blog Scraper: Error processing request:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        totalPostsScraped: 0,
        tasksProcessed: 0,
        taskResults: []
      } as BlogScrapingResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
});
