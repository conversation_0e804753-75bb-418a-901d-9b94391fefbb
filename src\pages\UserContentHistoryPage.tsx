import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import UserContentHistory from '@/components/UserContentHistory';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';

const UserContentHistoryPage = () => {
  const { language } = useLanguage();
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            to={language === 'zh' ? '/zh' : '/'}
            className="inline-flex items-center text-muted-foreground hover:text-foreground mb-4 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back')}
          </Link>

          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-2">{t('contentHistory.title')}</h1>
          </div>
        </div>

        {/* Content */}
        <UserContentHistory />
      </div>

      <Footer />
    </div>
  );
};

export default UserContentHistoryPage;
