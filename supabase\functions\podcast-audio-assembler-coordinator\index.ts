import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PodcastTask {
  id: string;
  status: string;
  created_at: string;
  updated_at: string | null;
}

// Configuration
const MAX_CONCURRENT_TASKS = 1; // Maximum concurrent audio-assembler instances
const TASK_TIMEOUT_MINUTES = 2; // Consider task stuck after 2 minutes (reduced for faster recovery)
const MAX_RETRY_ATTEMPTS = 3; // Maximum number of retry attempts before marking as failed
const COORDINATOR_ID = `coordinator-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🎵 Podcast Audio Assembler Coordinator started');

    // Create Supabase client with service role key
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Step 1: Check for stuck tasks and reset them
    await resetStuckTasks(supabaseClient);

    // Step 2: Check current running tasks
    const runningTasks = await getCurrentRunningTasks(supabaseClient);
    console.log(`📊 Currently running tasks: ${runningTasks.length}/${MAX_CONCURRENT_TASKS}`);

    if (runningTasks.length >= MAX_CONCURRENT_TASKS) {
      console.log('⏸️ Maximum concurrent tasks reached, skipping this run');
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: `Maximum concurrent tasks (${MAX_CONCURRENT_TASKS}) reached`,
          running_tasks: runningTasks.length
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      );
    }

    // Step 3: Find available tasks to process
    const availableSlots = MAX_CONCURRENT_TASKS - runningTasks.length;
    const pendingTasks = await getPendingTasks(supabaseClient, availableSlots);

    // Debug: Log all tasks with tts_ready status
    const { data: allTtsReadyTasks, error: debugError } = await supabaseClient
      .from('podcast_tasks')
      .select('id, status, created_at, updated_at')
      .eq('status', 'tts_ready');

    console.log(`🔍 Debug: All tts_ready tasks in database:`, allTtsReadyTasks);
    console.log(`📋 Found ${pendingTasks.length} pending tasks from ${allTtsReadyTasks?.length || 0} tts_ready tasks`);

    if (debugError) {
      console.error('❌ Debug query error:', debugError);
    }

    if (pendingTasks.length === 0) {
      console.log('✅ No pending audio assembly tasks found');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No pending tasks',
          running_tasks: runningTasks.length,
          debug_tts_ready_tasks: allTtsReadyTasks?.length || 0
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      );
    }

    console.log(`🚀 Found ${pendingTasks.length} pending tasks, starting processing...`);

    // Step 4: Start processing tasks
    const results = [];
    for (const task of pendingTasks) {
      try {
        const result = await startAudioAssemblerTask(task, supabaseClient);
        results.push(result);
        console.log(`✅ Started task ${task.id}: ${result.success ? 'Success' : 'Failed'}`);
      } catch (error) {
        console.error(`❌ Failed to start task ${task.id}:`, error);
        results.push({ task_id: task.id, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`🎯 Coordinator completed: ${successCount}/${results.length} tasks started successfully`);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Started ${successCount} audio assembly tasks`,
        results: results,
        running_tasks_before: runningTasks.length,
        running_tasks_after: runningTasks.length + successCount
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );

  } catch (error) {
    console.error('❌ Coordinator error:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    );
  }
});

// Function to call Zeabur audio assembler
async function callZeaburAssembler(taskId: string, segments: any[]): Promise<any> {
  const zeaburUrl = Deno.env.get('ZEABUR_AUDIO_ASSEMBLER_URL');
  const apiKey = Deno.env.get('ZEABUR_API_SECRET_KEY');

  if (!zeaburUrl || !apiKey) {
    throw new Error('Zeabur assembler configuration missing (ZEABUR_AUDIO_ASSEMBLER_URL or ZEABUR_API_SECRET_KEY)');
  }

  const assemblerUrl = `${zeaburUrl}/api/assemble`;
  console.log(`🌐 Calling Zeabur assembler: ${assemblerUrl}`);

  const response = await fetch(assemblerUrl, {
    method: 'POST',
    headers: {
      'X-API-Key': apiKey,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      task_id: taskId,
      segments: segments
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Zeabur assembler returned ${response.status}: ${errorText}`);
  }

  return await response.json();
}

// Reset tasks that have been stuck in audio_assembling state with retry limit
async function resetStuckTasks(supabaseClient: any): Promise<void> {
  const stuckThreshold = new Date(Date.now() - TASK_TIMEOUT_MINUTES * 60 * 1000).toISOString();

  const { data: stuckTasks, error } = await supabaseClient
    .from('podcast_tasks')
    .select('id, status, updated_at, retry_count')
    .eq('status', 'audio_assembling')
    .lt('updated_at', stuckThreshold);

  if (error) {
    console.error('❌ Error checking stuck tasks:', error);
    return;
  }

  if (stuckTasks && stuckTasks.length > 0) {
    console.log(`🔄 Found ${stuckTasks.length} stuck tasks, processing...`);

    // Separate tasks by retry count
    const tasksToRetry = stuckTasks.filter(task => (task.retry_count || 0) < MAX_RETRY_ATTEMPTS);
    const tasksToFail = stuckTasks.filter(task => (task.retry_count || 0) >= MAX_RETRY_ATTEMPTS);

    // Reset tasks that haven't exceeded retry limit
    if (tasksToRetry.length > 0) {
      console.log(`🔄 Retrying ${tasksToRetry.length} tasks (retry count < ${MAX_RETRY_ATTEMPTS})`);

      for (const task of tasksToRetry) {
        const newRetryCount = (task.retry_count || 0) + 1;
        await supabaseClient
          .from('podcast_tasks')
          .update({
            status: 'tts_ready',
            retry_count: newRetryCount,
            updated_at: new Date().toISOString(),
            error_message: `Reset by coordinator - attempt ${newRetryCount}/${MAX_RETRY_ATTEMPTS} (stuck since ${stuckThreshold})`
          })
          .eq('id', task.id);
      }
    }

    // Mark tasks as failed that have exceeded retry limit
    if (tasksToFail.length > 0) {
      console.log(`❌ Marking ${tasksToFail.length} tasks as failed (retry count >= ${MAX_RETRY_ATTEMPTS})`);

      for (const task of tasksToFail) {
        await supabaseClient
          .from('podcast_tasks')
          .update({
            status: 'failed',
            updated_at: new Date().toISOString(),
            error_message: `Task failed after ${MAX_RETRY_ATTEMPTS} attempts - exceeded retry limit`
          })
          .eq('id', task.id);
      }
    }

    console.log(`✅ Processed stuck tasks: ${tasksToRetry.length} retried, ${tasksToFail.length} marked as failed`);
  }
}

// Get currently running tasks
async function getCurrentRunningTasks(supabaseClient: any): Promise<PodcastTask[]> {
  const { data: runningTasks, error } = await supabaseClient
    .from('podcast_tasks')
    .select('id, status, created_at, updated_at')
    .eq('status', 'audio_assembling');

  if (error) {
    console.error('❌ Error getting running tasks:', error);
    return [];
  }

  return runningTasks || [];
}

// Get pending tasks that need processing
async function getPendingTasks(supabaseClient: any, limit: number): Promise<PodcastTask[]> {
  const { data: pendingTasks, error } = await supabaseClient
    .from('podcast_tasks')
    .select('id, status, created_at, updated_at')
    .eq('status', 'tts_ready')
    .order('created_at', { ascending: true })
    .limit(limit);

  if (error) {
    console.error('❌ Error getting pending tasks:', error);
    return [];
  }

  return pendingTasks || [];
}

// Start audio assembler task
async function startAudioAssemblerTask(task: PodcastTask, supabaseClient: any): Promise<any> {
  // Step 1: Atomically claim the task
  const { data: claimedTask, error: claimError } = await supabaseClient
    .from('podcast_tasks')
    .update({
      status: 'audio_assembling',
      updated_at: new Date().toISOString()
    })
    .eq('id', task.id)
    .eq('status', 'tts_ready') // Only update if still ready for assembly
    .select()
    .single();

  if (claimError || !claimedTask) {
    console.log(`⚠️ Task ${task.id} was already claimed by another coordinator`);
    return { task_id: task.id, success: false, error: 'Task already claimed' };
  }

  console.log(`🔒 Successfully claimed task ${task.id}`);

  // Step 2: Get completed segments for the task
  const { data: segments, error: segmentsError } = await supabaseClient
    .from('podcast_segments')
    .select('id, segment_index, speaker, audio_path, audio_size_bytes')
    .eq('task_id', task.id)
    .eq('status', 'completed')
    .order('segment_index', { ascending: true });

  if (segmentsError) {
    throw new Error(`Failed to fetch segments: ${segmentsError.message}`);
  }

  if (!segments || segments.length === 0) {
    throw new Error('No completed audio segments found for assembly');
  }

  console.log(`📊 Found ${segments.length} completed segments for task ${task.id}`);

  // Step 3: Call Zeabur audio assembler
  try {
    console.log(`🚀 Calling Zeabur audio assembler for task ${task.id}`);
    const result = await callZeaburAssembler(task.id, segments);

    console.log(`✅ Audio assembler started for task ${task.id}`);

    return {
      task_id: task.id,
      success: true,
      message: 'Audio assembler started successfully',
      assembler_response: result,
      segments_count: segments.length
    };

  } catch (error) {
    console.error(`❌ Failed to start audio assembler for task ${task.id}:`, error);

    // Get current retry count
    const { data: currentTask } = await supabaseClient
      .from('podcast_tasks')
      .select('retry_count')
      .eq('id', task.id)
      .single();

    const currentRetryCount = currentTask?.retry_count || 0;
    const newRetryCount = currentRetryCount + 1;

    // Check if we should retry or mark as failed
    if (newRetryCount >= MAX_RETRY_ATTEMPTS) {
      // Mark as failed - exceeded retry limit
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'failed',
          retry_count: newRetryCount,
          error_message: `Task failed after ${MAX_RETRY_ATTEMPTS} attempts: ${error.message}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      console.log(`❌ Task ${task.id} marked as failed after ${MAX_RETRY_ATTEMPTS} attempts`);
    } else {
      // Reset for retry
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'tts_ready',
          retry_count: newRetryCount,
          error_message: `Coordinator failed to start assembler (attempt ${newRetryCount}/${MAX_RETRY_ATTEMPTS}): ${error.message}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      console.log(`🔄 Task ${task.id} reset for retry (attempt ${newRetryCount}/${MAX_RETRY_ATTEMPTS})`);
    }

    return {
      task_id: task.id,
      success: false,
      error: error.message,
      retry_count: newRetryCount,
      final_failure: newRetryCount >= MAX_RETRY_ATTEMPTS
    };
  }
}
