import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Copy,
  ExternalLink,
  Clock,
  Loader2,
  AlertCircle,
  FileText,
  X,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Pause,
  Play,
  RotateCcw,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { supabase } from '@/integrations/supabase/client';
import { renderMarkdown } from '@/utils/markdown';
import PlatformMultiSelect from '@/components/PlatformMultiSelect';

interface TaskStatus {
  task_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress?: number;
  queue_position?: number;
  estimated_completion?: string;
  result_ids?: string[];
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  target_platforms: string[];
  style: string;
  user_input?: string;
  retry_count: number;
  max_retries: number;
  summary_info?: {
    id: string;
    content: string;
    platform: string;
    source_name: string;
    topic_name?: string;
    source_urls?: string[];
    title?: string;
    url?: string;
    created_at?: string;
  };
  generated_content?: Array<{
    id: string;
    target_platform: string;
    content: string;
    created_at: string;
  }>;
}



// These will be replaced with translation functions in the component

// Helper function to get platform from summary_type
const getPlatformFromSummaryType = (summaryType: string): string => {
  const typeMap: { [key: string]: string } = {
    'blog_post': 'blog',
    'daily_subreddit': 'reddit',
    'youtube_video': 'youtube',
    'podcast': 'podcast',
    'wechat_post': 'wechat',
    'twitter_rss_datasource': 'twitter-rss',
    'xiaohongshu_post': 'xiaohongshu'
  };
  return typeMap[summaryType] || 'unknown';
};

const UserContentHistory: React.FC = () => {
  const { t } = useTranslation();

  // Translation functions for platform and style names
  const getPlatformName = (platform: string): string => {
    const platformMap: { [key: string]: string } = {
      blog: t('platforms.blog', 'Blog'),
      linkedin: 'LinkedIn',
      twitter: 'Twitter',
      reddit: 'Reddit',
      xiaohongshu: t('platforms.xiaohongshu', 'Rednote'),
      wechat: t('platforms.wechat', 'WeChat'),
      youtube: 'YouTube',
      podcast: t('platforms.podcast', 'Podcast')
    };
    return platformMap[platform] || platform;
  };

  const getStyleName = (style: string): string => {
    const styleMap: { [key: string]: string } = {
      engaging: t('styles.engaging', 'Engaging'),
      professional: t('styles.professional', 'Professional'),
      casual: t('styles.casual', 'Casual'),
      creative: t('styles.creative', 'Creative'),
      analytical: t('styles.analytical', 'Analytical')
    };
    return styleMap[style] || style;
  };

  const [tasks, setTasks] = useState<TaskStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSearchQuery, setActiveSearchQuery] = useState(''); // Actual search query for API calls
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sourcePlatformFilter, setSourcePlatformFilter] = useState<string[]>(['all']);
  const [targetPlatformFilter, setTargetPlatformFilter] = useState<string[]>(['all']);
  const [styleFilter, setStyleFilter] = useState<string>('all');
  const [topicFilter, setTopicFilter] = useState<string>('all');
  const [sourceFilter, setSourceFilter] = useState<string>('all');
  const [queueStats, setQueueStats] = useState<any>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const [topics, setTopics] = useState<Array<{id: string, name: string}>>([]);
  const [sources, setSources] = useState<Array<{name: string}>>([]);
  const { toast } = useToast();
  const { user } = useAuth();

  // Handle search submission
  const handleSearchSubmit = () => {
    setActiveSearchQuery(searchQuery);
  };

  // Handle enter key search
  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearchSubmit();
    }
  };

  // Fetch topics list
  const fetchTopics = async () => {
    try {
      const { data, error } = await supabase
        .from('topics')
        .select('id, name')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setTopics(data || []);
    } catch (err) {
      console.error('Error fetching topics:', err);
    }
  };

  // Fetch data sources list
  const fetchSources = async () => {
    try {
      // 从任务中提取唯一的数据源
      const uniqueSources = Array.from(
        new Set(
          tasks
            .filter(task => task.summary_info?.source_name)
            .map(task => task.summary_info!.source_name)
        )
      ).map(name => ({ name }));

      setSources(uniqueSources);
    } catch (err) {
      console.error('Error fetching sources:', err);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchTasks();
      fetchTopics();
      // Set up polling to update task status
      const interval = setInterval(fetchTasks, 5000); // 每5秒更新一次
      setPollingInterval(interval);

      return () => {
        if (interval) clearInterval(interval);
      };
    }
  }, [user?.id, statusFilter, sourcePlatformFilter, targetPlatformFilter, styleFilter, topicFilter, sourceFilter, activeSearchQuery]);

  useEffect(() => {
    return () => {
      if (pollingInterval) clearInterval(pollingInterval);
    };
  }, [pollingInterval]);

  // Update data sources list when tasks are updated
  useEffect(() => {
    fetchSources();
  }, [tasks]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is logged in
      if (!user?.id) {
        setError(t('contentHistory.errors.userNotLoggedIn'));
        setLoading(false);
        return;
      }

      // Query tasks and related information directly from database
      const { data: tasksData, error: tasksError } = await supabase
        .from('content_generation_queue')
        .select(`
          id,
          status,
          target_platforms,
          style,
          user_input,
          created_at,
          started_at,
          completed_at,
          result_ids,
          error_message,
          retry_count,
          max_retries,
          summary_id,
          summaries!inner(
            id,
            content,
            source_urls,
            metadata,
            summary_type,
            created_at
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(25);

      if (tasksError) throw tasksError;

      // Transform data format
      let filteredTasks: TaskStatus[] = (tasksData || []).map(task => {
        const summary = task.summaries as any;
        const metadata = summary?.metadata || {};

        // Intelligently get platform information using multiple fallback options
        const platform = metadata.platform ||
                         getPlatformFromSummaryType(summary?.summary_type) ||
                         'unknown';

        return {
          task_id: task.id,
          status: task.status,
          target_platforms: task.target_platforms,
          style: task.style,
          user_input: task.user_input,
          created_at: task.created_at,
          started_at: task.started_at,
          completed_at: task.completed_at,
          result_ids: task.result_ids,
          error_message: task.error_message,
          retry_count: task.retry_count || 0,
          max_retries: task.max_retries || 3,
          summary_info: summary ? {
            id: summary.id,
            content: summary.content,
            platform: platform,
            source_name: metadata.source_name || 'unknown',
            topic_name: metadata.topic_name,
            source_urls: summary.source_urls || [],
            title: metadata.post_title,
            url: metadata.post_url,
            created_at: summary.created_at
          } : undefined
        };
      });

      // Apply filters
      if (statusFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task => task.status === statusFilter);
      }
      if (sourcePlatformFilter.length > 0 && !sourcePlatformFilter.includes('all')) {
        filteredTasks = filteredTasks.filter(task =>
          task.summary_info?.platform && sourcePlatformFilter.includes(task.summary_info.platform)
        );
      }
      if (targetPlatformFilter.length > 0 && !targetPlatformFilter.includes('all')) {
        filteredTasks = filteredTasks.filter(task =>
          task.target_platforms.some(platform => targetPlatformFilter.includes(platform))
        );
      }
      if (styleFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task => task.style === styleFilter);
      }
      if (topicFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task =>
          task.summary_info?.topic_name === topicFilter ||
          topics.find(t => t.id === topicFilter)?.name === task.summary_info?.topic_name
        );
      }
      if (sourceFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task =>
          task.summary_info?.source_name === sourceFilter
        );
      }
      if (activeSearchQuery.trim()) {
        const query = activeSearchQuery.toLowerCase();
        filteredTasks = filteredTasks.filter(task =>
          task.summary_info?.content?.toLowerCase().includes(query) ||
          task.summary_info?.source_name?.toLowerCase().includes(query) ||
          task.summary_info?.title?.toLowerCase().includes(query) ||
          task.user_input?.toLowerCase().includes(query)
        );
      }

      // Get generated content for completed tasks
      for (const task of filteredTasks) {
        if (task.status === 'completed' && task.result_ids && task.result_ids.length > 0) {
          await fetchGeneratedContent(task);
        }
      }

      setTasks(filteredTasks);

      // Calculate queue statistics
      const stats = {
        total_pending: filteredTasks.filter(t => t.status === 'pending').length,
        total_processing: filteredTasks.filter(t => t.status === 'processing').length,
        total_completed: filteredTasks.filter(t => t.status === 'completed').length,
        total_failed: filteredTasks.filter(t => t.status === 'failed').length,
        estimated_wait_time_minutes: filteredTasks.filter(t => t.status === 'pending').length * 2
      };
      setQueueStats(stats);

    } catch (error: any) {
      console.error('Error fetching tasks:', error);
      setError(error.message || t('contentHistory.loading'));
    } finally {
      setLoading(false);
    }
  };

  const fetchGeneratedContent = async (task: TaskStatus) => {
    try {
      const { data, error } = await supabase
        .from('user_generated_content')
        .select('id, target_platform, content, created_at')
        .in('id', task.result_ids || []);

      if (!error && data) {
        task.generated_content = data;
      }
    } catch (error) {
      console.warn('Failed to fetch generated content for task:', task.task_id, error);
    }
  };



  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: t('contentHistory.actions.copied'),
        description: t('contentHistory.actions.copiedDesc'),
      });
    } catch (error) {
      toast({
        title: t('contentHistory.actions.copyFailed'),
        description: t('contentHistory.actions.copyFailedDesc'),
        variant: 'destructive',
      });
    }
  };

  const deleteContent = async (contentId: string, taskId: string) => {
    // Show confirmation dialog
    if (!confirm(t('contentHistory.actions.deleteConfirm'))) {
      return;
    }

    try {
      // Delete from user_generated_content table
      const { error: deleteError } = await supabase
        .from('user_generated_content')
        .delete()
        .eq('id', contentId);

      if (deleteError) throw deleteError;

      // Find the task and update its result_ids
      const { data: taskData, error: taskFetchError } = await supabase
        .from('content_generation_queue')
        .select('result_ids')
        .eq('id', taskId)
        .single();

      if (taskFetchError) {
        console.error('Error fetching task data:', taskFetchError);
      } else if (taskData) {
        const currentResultIds = taskData.result_ids || [];
        const updatedResultIds = currentResultIds.filter((id: string) => id !== contentId);

        if (updatedResultIds.length === 0) {
          // If no more content, delete the entire task
          const { error: taskDeleteError } = await supabase
            .from('content_generation_queue')
            .delete()
            .eq('id', taskId);

          if (taskDeleteError) {
            console.error('Error deleting task:', taskDeleteError);
          }
        } else {
          // Update the task with remaining result_ids
          const { error: taskUpdateError } = await supabase
            .from('content_generation_queue')
            .update({ result_ids: updatedResultIds })
            .eq('id', taskId);

          if (taskUpdateError) {
            console.error('Error updating task result_ids:', taskUpdateError);
          }
        }
      }

      toast({
        title: t('contentHistory.actions.deleteSuccess'),
        description: t('contentHistory.actions.deleteSuccessDesc'),
      });

      // Update local state
      setTasks(prevTasks => {
        return prevTasks.map(task => {
          if (task.id === taskId && task.generated_content) {
            const updatedContent = task.generated_content.filter(content => content.id !== contentId);

            // If no more content, we should remove this task from the list
            // But since we're filtering, we'll return null and filter it out
            if (updatedContent.length === 0) {
              return null;
            }

            return {
              ...task,
              generated_content: updatedContent
            };
          }
          return task;
        }).filter(task => task !== null) as TaskStatus[];
      });

    } catch (error: any) {
      console.error('Error deleting content:', error);
      toast({
        title: t('contentHistory.actions.deleteFailed'),
        description: error.message || t('contentHistory.actions.deleteFailedDesc'),
        variant: 'destructive',
      });
    }
  };

  const clearFilters = () => {
    setSearchQuery('');
    setActiveSearchQuery('');
    setStatusFilter('all');
    setSourcePlatformFilter(['all']);
    setTargetPlatformFilter(['all']);
    setStyleFilter('all');
    setTopicFilter('all');
    setSourceFilter('all');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processing':
        return <Play className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <Pause className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return t('contentHistory.status.pending');
      case 'processing':
        return t('contentHistory.status.processing');
      case 'completed':
        return t('contentHistory.status.completed');
      case 'failed':
        return t('contentHistory.status.failed');
      case 'cancelled':
        return t('contentHistory.status.cancelled');
      default:
        return status;
    }
  };

  if (loading && tasks.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-muted-foreground">{t('contentHistory.loading')}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">{t('contentHistory.errors.loadFailed')}</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={fetchTasks}>{t('contentHistory.actions.retry')}</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Queue Statistics */}
      {queueStats && (
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <div>
                  <div className="text-2xl font-bold">{queueStats.total_pending}</div>
                  <div className="text-sm text-muted-foreground">{t('contentHistory.status.pending')}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-2">
                <Play className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-2xl font-bold">{queueStats.total_processing}</div>
                  <div className="text-sm text-muted-foreground">{t('contentHistory.status.processing')}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-2xl font-bold">{queueStats.total_completed}</div>
                  <div className="text-sm text-muted-foreground">{t('contentHistory.status.completed')}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-500" />
                <div>
                  <div className="text-2xl font-bold">{queueStats.total_failed}</div>
                  <div className="text-sm text-muted-foreground">{t('contentHistory.status.failed')}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t('contentHistory.filters.filterAndSearch')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* First row: Search box + Status + Target Platform + Style */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder={t('contentHistory.filters.searchPlaceholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleSearchKeyPress}
                  className="pl-10 pr-20"
                />
                <Button
                  onClick={handleSearchSubmit}
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8"
                >
                  {t('contentHistory.filters.search')}
                </Button>
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('contentHistory.filters.status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('contentHistory.filters.allStatuses')}</SelectItem>
                  <SelectItem value="pending">{t('contentHistory.status.pending')}</SelectItem>
                  <SelectItem value="processing">{t('contentHistory.status.processing')}</SelectItem>
                  <SelectItem value="completed">{t('contentHistory.status.completed')}</SelectItem>
                  <SelectItem value="failed">{t('contentHistory.status.failed')}</SelectItem>
                  <SelectItem value="cancelled">{t('contentHistory.status.cancelled')}</SelectItem>
                </SelectContent>
              </Select>

              <PlatformMultiSelect
                selectedPlatforms={targetPlatformFilter}
                onPlatformChange={setTargetPlatformFilter}
                placeholder={t('contentHistory.filters.targetPlatform')}
                allPlatformsText={t('contentHistory.filters.allPlatforms')}
              />

              <Select value={styleFilter} onValueChange={setStyleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('contentHistory.filters.style')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('contentHistory.filters.allStyles')}</SelectItem>
                  {['engaging', 'professional', 'casual', 'creative', 'analytical'].map((key) => (
                    <SelectItem key={key} value={key}>{getStyleName(key)}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Second row: Data Source Platform + Topic + Data Source + Clear Filters */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <PlatformMultiSelect
                selectedPlatforms={sourcePlatformFilter}
                onPlatformChange={setSourcePlatformFilter}
                placeholder={t('contentHistory.filters.platform')}
                allPlatformsText={t('contentHistory.filters.allPlatforms')}
              />

              <Select value={topicFilter} onValueChange={setTopicFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('contentHistory.filters.topic')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('contentHistory.filters.allTopics')}</SelectItem>
                  {topics.map(topic => (
                    <SelectItem key={topic.id} value={topic.id}>
                      {topic.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sourceFilter} onValueChange={setSourceFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('contentHistory.filters.dataSource')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('contentHistory.filters.allDataSources')}</SelectItem>
                  {sources.map(source => (
                    <SelectItem key={source.name} value={source.name}>
                      {source.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button onClick={clearFilters} variant="outline" className="flex items-center gap-2">
                <X className="h-4 w-4" />
                {t('contentHistory.filters.clearFilters')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Task List */}
      {tasks.length > 0 ? (
        <div className="space-y-4">
          {tasks.map((task) => (
            <Card key={task.task_id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(task.status)}
                    <Badge variant={task.status === 'completed' ? 'default' : 'secondary'}>
                      {getStatusText(task.status)}
                    </Badge>
                    <Badge variant="secondary">
                      {getStyleName(task.style)}
                    </Badge>
                    {task.summary_info?.platform && (
                      <Badge variant="outline">
                        {task.summary_info.platform}
                      </Badge>
                    )}
                    {task.summary_info?.topic_name && (
                      <Badge variant="outline">
                        {task.summary_info.topic_name}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    {new Date(task.created_at).toLocaleString()}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* 1. Generation content related information */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-blue-900 mb-3">{t('contentHistory.labels.generationInfo')}</h3>
                    <div className="space-y-3">
                      {/* Task status */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm font-medium text-muted-foreground mb-1">{t('contentHistory.labels.taskStatus')}</div>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(task.status)}
                            <span className="text-sm">{getStatusText(task.status)}</span>
                            {task.queue_position && (
                              <span className="text-xs text-muted-foreground">
                                ({t('contentHistory.labels.queuePosition')}: {task.queue_position})
                              </span>
                            )}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground mb-1">{t('contentHistory.labels.targetPlatform')}</div>
                          <div className="flex flex-wrap gap-1">
                            {task.target_platforms.map((platform) => (
                              <Badge key={platform} variant="outline" className="text-xs">
                                {getPlatformName(platform)}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Style and user ideas */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm font-medium text-muted-foreground mb-1">{t('contentHistory.labels.generationStyle')}</div>
                          <Badge variant="secondary">
                            {getStyleName(task.style)}
                          </Badge>
                        </div>
                        {task.user_input && (
                          <div>
                            <div className="text-sm font-medium text-muted-foreground mb-1">{t('contentHistory.labels.userIdea')}</div>
                            <div className="text-sm text-foreground">
                              {task.user_input}
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Completion time */}
                      {task.completed_at && (
                        <div>
                          <div className="text-sm font-medium text-muted-foreground mb-1">{t('contentHistory.labels.completionTime')}</div>
                          <div className="text-sm text-foreground">
                            {new Date(task.completed_at).toLocaleString()}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 2. Data source related information */}
                  {task.summary_info && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h3 className="text-sm font-semibold text-green-900 mb-3">{t('contentHistory.labels.dataSourceInfo')}</h3>
                      <div className="space-y-3">
                        {/* Article title and URL */}
                        {task.summary_info.title && (
                          <div>
                            <div className="text-sm font-medium text-muted-foreground mb-1">{t('contentHistory.labels.articleTitle')}</div>
                            <div className="text-sm font-medium text-foreground">
                              {task.summary_info.title}
                            </div>
                          </div>
                        )}

                        {task.summary_info.url && (
                          <div>
                            <div className="text-sm font-medium text-muted-foreground mb-1">{t('contentHistory.labels.articleLink')}</div>
                            <a
                              href={task.summary_info.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm text-blue-600 hover:text-blue-800 underline break-all"
                            >
                              {task.summary_info.url}
                            </a>
                          </div>
                        )}

                        {/* Data source basic information */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-muted-foreground">{t('contentHistory.labels.dataSource')}: </span>
                            <span className="text-foreground">{task.summary_info.source_name}</span>
                          </div>
                          <div>
                            <span className="font-medium text-muted-foreground">{t('contentHistory.labels.dataSourcePlatform')}: </span>
                            <span className="text-foreground">{getPlatformName(task.summary_info.platform)}</span>
                          </div>
                          <div>
                            <span className="font-medium text-muted-foreground">{t('contentHistory.labels.dataSourceTopic')}: </span>
                            <span className="text-foreground">{task.summary_info.topic_name || t('contentHistory.labels.unknown')}</span>
                          </div>
                        </div>

                        {/* Original links count */}
                        {task.summary_info.source_urls && task.summary_info.source_urls.length > 0 && (
                          <div className="text-xs text-muted-foreground">
                            {t('contentHistory.labels.sourceLinksCount', { count: task.summary_info.source_urls.length })}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Task progress and time information */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-muted-foreground">
                    <div>
                      <span className="font-medium">{t('contentHistory.labels.creationTime')}:</span><br />
                      {new Date(task.created_at).toLocaleString()}
                    </div>
                    {task.started_at && (
                      <div>
                        <span className="font-medium">{t('contentHistory.labels.startTime')}:</span><br />
                        {new Date(task.started_at).toLocaleString()}
                      </div>
                    )}
                    {task.summary_info?.created_at && (
                      <div>
                        <span className="font-medium">{t('contentHistory.labels.dataSourcePublishTime')}:</span><br />
                        {new Date(task.summary_info.created_at).toLocaleString()}
                      </div>
                    )}
                  </div>

                  {/* Error information */}
                  {task.status === 'failed' && task.error_message && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="text-sm font-medium text-red-800 mb-1">{t('contentHistory.labels.errorMessage')}:</div>
                      <div className="text-sm text-red-700">{task.error_message}</div>
                      {task.retry_count > 0 && (
                        <div className="text-xs text-red-600 mt-1">
                          {t('contentHistory.labels.retryCount', { current: task.retry_count, max: task.max_retries })}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Generated content */}
                  {task.status === 'completed' && task.generated_content && task.generated_content.length > 0 && (
                    <div>
                      <div className="text-sm font-medium mb-2">{t('contentHistory.labels.generatedContent')}:</div>
                      <div className="space-y-3">
                        {task.generated_content.map((content) => (
                          <div key={content.id} className="border rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <Badge variant="outline">
                                {getPlatformName(content.target_platform)}
                              </Badge>
                              <div className="flex items-center gap-2">
                                <Button
                                  onClick={() => copyToClipboard(content.content)}
                                  variant="outline"
                                  size="sm"
                                >
                                  <Copy className="h-4 w-4 mr-2" />
                                  {t('contentHistory.actions.copy')}
                                </Button>
                                <Button
                                  onClick={() => deleteContent(content.id, task.id)}
                                  variant="outline"
                                  size="sm"
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  {t('contentHistory.actions.delete')}
                                </Button>
                              </div>
                            </div>
                            <div
                              className="text-sm leading-relaxed max-w-none markdown-content"
                              dangerouslySetInnerHTML={{
                                __html: renderMarkdown(content.content)
                              }}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">{t('contentHistory.empty.title')}</h3>
          <p className="text-muted-foreground">
            {t('contentHistory.empty.description')}
          </p>
        </div>
      )}
    </div>
  );
};

export default UserContentHistory;
