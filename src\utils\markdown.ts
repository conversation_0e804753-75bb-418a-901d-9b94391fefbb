/**
 * Unified Markdown rendering utility
 * Fixes the double bullet point issue in list items
 */

export const renderMarkdown = (content: string): string => {
  if (!content) return '';

  // Clean up markdown code blocks
  let cleanContent = content
    .replace(/^```markdown\s*\n?/i, '')
    .replace(/\n?```\s*$/i, '')
    .trim();

  return cleanContent
    // Headers (process before list items to avoid conflicts)
    .replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mt-4 mb-2">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-4 mb-2">$1</h1>')
    // Unordered list items - handle both - and * with optional indentation
    .replace(/^(\s*)[-*]\s+(.*$)/gim, (match, indent, content) => {
      const indentLevel = Math.floor(indent.length / 4) + 1;
      const marginClass = `ml-${indentLevel * 6}`;
      return `<li class="${marginClass} list-disc list-inside">${content}</li>`;
    })
    // Bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Italic text (process after list items to avoid conflicts)
    .replace(/\*([^*\s][^*]*[^*\s])\*/g, '<em>$1</em>')
    .replace(/\*([^*\s])\*/g, '<em>$1</em>')
    // Ordered list items
    .replace(/^(\s*)\d+\.\s+(.*$)/gim, (match, indent, content) => {
      const indentLevel = Math.floor(indent.length / 4) + 1;
      const marginClass = `ml-${indentLevel * 6}`;
      return `<li class="${marginClass} list-decimal list-inside">${content}</li>`;
    })
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">$1</a>')
    // Line breaks
    .replace(/\n\n/g, '<br><br>')
    .replace(/\n/g, '<br>');
};

/**
 * Alternative rendering for summary content with proper list handling
 */
export const renderSummaryMarkdown = (content: string): string => {
  if (!content) return '';

  return content
    // Headers (process before list items to avoid conflicts)
    .replace(/###\s+(.*?)(?=\n|$)/g, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
    .replace(/##\s+(.*?)(?=\n|$)/g, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
    .replace(/#\s+(.*?)(?=\n|$)/g, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
    // Unordered list items - handle both - and * with optional indentation
    .replace(/^(\s*)[-*]\s+(.*?)(?=\n|$)/gm, (match, indent, content) => {
      const indentLevel = Math.floor(indent.length / 4) + 1;
      const marginClass = `ml-${indentLevel * 6}`;
      return `<li class="${marginClass} list-disc list-inside">${content}</li>`;
    })
    // Bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Italic text (process after list items to avoid conflicts)
    .replace(/\*([^*\s][^*]*[^*\s])\*/g, '<em>$1</em>')
    .replace(/\*([^*\s])\*/g, '<em>$1</em>')
    // Ordered list items
    .replace(/^(\s*)\d+\.\s+(.*?)(?=\n|$)/gm, (match, indent, content) => {
      const indentLevel = Math.floor(indent.length / 4) + 1;
      const marginClass = `ml-${indentLevel * 6}`;
      return `<li class="${marginClass} list-decimal list-inside">${content}</li>`;
    });
};
