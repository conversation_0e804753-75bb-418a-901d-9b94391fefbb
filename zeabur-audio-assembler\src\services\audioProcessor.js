import ffmpeg from 'fluent-ffmpeg';
import fs from 'fs/promises';
import path from 'path';
import { logger } from '../utils/logger.js';
import { supabaseService } from './supabase.js';

export class AudioProcessor {
  constructor() {
    this.tempDir = process.env.TEMP_DIR || '/tmp/audio-processing';
    this.maxConcurrentTasks = parseInt(process.env.MAX_CONCURRENT_TASKS) || 3;
    this.activeTasks = new Map();
    
    // 确保临时目录存在
    this.ensureTempDir();
  }

  async ensureTempDir() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      logger.error('Failed to create temp directory:', error);
    }
  }

  // 主要的音频合并方法
  async assembleAudio(taskId, segments) {
    if (this.activeTasks.has(taskId)) {
      throw new Error(`Task ${taskId} is already being processed`);
    }

    if (this.activeTasks.size >= this.maxConcurrentTasks) {
      throw new Error('Maximum concurrent tasks reached');
    }

    this.activeTasks.set(taskId, { status: 'processing', startTime: Date.now() });

    try {
      // 更新任务状态为处理中
      await supabaseService.updateTaskStatus(taskId, 'audio_assembling');

      // 按segment_index排序
      const sortedSegments = segments.sort((a, b) => a.segment_index - b.segment_index);
      
      logger.info(`🎵 Processing ${sortedSegments.length} segments for task ${taskId}`);

      // 下载所有音频片段
      const downloadedFiles = await this.downloadSegments(taskId, sortedSegments);

      // 合并音频文件
      const finalAudioPath = await this.mergeAudioFiles(taskId, downloadedFiles);

      // 上传最终文件
      const uploadResult = await this.uploadFinalAudio(taskId, finalAudioPath);

      // 清理临时文件
      await this.cleanupTempFiles(taskId, downloadedFiles, finalAudioPath);

      // 更新任务状态为完成
      await supabaseService.updateTaskStatus(taskId, 'completed', {
        audio_url: uploadResult.path,
        completed_at: new Date().toISOString()
      });

      this.activeTasks.delete(taskId);

      return {
        audio_url: uploadResult.path,
        duration_seconds: uploadResult.duration,
        file_size_bytes: uploadResult.size
      };

    } catch (error) {
      this.activeTasks.delete(taskId);
      
      // 更新任务状态为失败
      try {
        await supabaseService.updateTaskStatus(taskId, 'failed', {
          error_message: error.message
        });
      } catch (updateError) {
        logger.error('Failed to update task status to failed:', updateError);
      }

      throw error;
    }
  }

  // 流式下载音频片段 (优化内存使用)
  async downloadSegments(taskId, segments) {
    const taskTempDir = path.join(this.tempDir, taskId);
    await fs.mkdir(taskTempDir, { recursive: true });

    const downloadedFiles = [];

    // 并发下载，但限制并发数量
    const DOWNLOAD_CONCURRENCY = 3;

    for (let i = 0; i < segments.length; i += DOWNLOAD_CONCURRENCY) {
      const batch = segments.slice(i, i + DOWNLOAD_CONCURRENCY);

      const batchPromises = batch.map(async (segment) => {
        const fileName = `segment_${segment.segment_index.toString().padStart(4, '0')}.mp3`;
        const localPath = path.join(taskTempDir, fileName);

        try {
          logger.debug(`📥 Streaming download segment ${segment.segment_index} from ${segment.audio_path}`);

          // 使用流式下载避免大文件占用内存
          const result = await supabaseService.downloadAudioStream(segment.audio_path, localPath);

          // 如果文件不存在，跳过该片段
          if (result === null) {
            logger.warn(`⚠️ Skipping missing segment ${segment.segment_index}`);
            return null;
          }

          return {
            localPath,
            segmentIndex: segment.segment_index,
            originalPath: segment.audio_path
          };

        } catch (error) {
          logger.error(`❌ Failed to download segment ${segment.segment_index}:`, error);
          throw new Error(`Failed to download segment ${segment.segment_index}: ${error.message}`);
        }
      });

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises);

      // 过滤掉跳过的片段 (null值)
      const validResults = batchResults.filter(result => result !== null);
      downloadedFiles.push(...validResults);

      const skippedCount = batchResults.length - validResults.length;
      if (skippedCount > 0) {
        logger.warn(`⚠️ Skipped ${skippedCount} missing segments in batch ${Math.floor(i / DOWNLOAD_CONCURRENCY) + 1}`);
      }

      logger.debug(`✅ Downloaded batch ${Math.floor(i / DOWNLOAD_CONCURRENCY) + 1}: ${validResults.length} segments (${skippedCount} skipped)`);
    }

    // 按segment_index排序确保正确顺序
    downloadedFiles.sort((a, b) => a.segmentIndex - b.segmentIndex);

    const totalSegments = segments.length;
    const downloadedCount = downloadedFiles.length;
    const skippedTotal = totalSegments - downloadedCount;

    logger.info(`📊 Download summary: ${downloadedCount}/${totalSegments} segments downloaded, ${skippedTotal} skipped`);

    if (downloadedCount === 0) {
      throw new Error('No audio segments were successfully downloaded');
    }

    return downloadedFiles;
  }

  // 使用FFmpeg concat协议合并音频文件 (更高效)
  async mergeAudioFiles(taskId, downloadedFiles) {
    return new Promise(async (resolve, reject) => {
      const outputPath = path.join(this.tempDir, taskId, 'final_podcast.mp3');
      const concatListPath = path.join(this.tempDir, taskId, 'concat_list.txt');

      logger.info(`🔗 Merging ${downloadedFiles.length} audio files for task ${taskId} using concat protocol`);

      try {
        // 创建concat列表文件
        const concatList = downloadedFiles
          .map(file => `file '${file.localPath.replace(/'/g, "'\\''")}'`)
          .join('\n');

        await fs.writeFile(concatListPath, concatList, 'utf8');
        logger.debug(`📝 Created concat list: ${concatListPath}`);

        // 使用FFmpeg concat协议 (比多输入更高效)
        const command = ffmpeg()
          .input(concatListPath)
          .inputOptions(['-f', 'concat', '-safe', '0'])
          .audioCodec('libmp3lame')  // 使用libmp3lame编码器
          .audioBitrate('128k')
          .audioChannels(2)
          .audioFrequency(44100)
          .output(outputPath)
          .on('start', (commandLine) => {
            logger.debug(`FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            if (progress.percent) {
              logger.debug(`Merging progress: ${Math.round(progress.percent)}%`);
            }
          })
          .on('end', async () => {
            // 清理concat列表文件
            try {
              await fs.unlink(concatListPath);
            } catch (cleanupError) {
              logger.warn(`Failed to cleanup concat list: ${cleanupError.message}`);
            }

            logger.info(`✅ Audio merging completed: ${outputPath}`);
            resolve(outputPath);
          })
          .on('error', async (error) => {
            // 清理concat列表文件
            try {
              await fs.unlink(concatListPath);
            } catch (cleanupError) {
              logger.warn(`Failed to cleanup concat list: ${cleanupError.message}`);
            }

            logger.error(`❌ FFmpeg error:`, error);
            reject(new Error(`Audio merging failed: ${error.message}`));
          });

        // 开始处理
        command.run();

      } catch (error) {
        logger.error(`❌ Failed to create concat list:`, error);
        reject(error);
      }
    });
  }

  // 上传最终音频文件
  async uploadFinalAudio(taskId, localAudioPath) {
    try {
      const audioBuffer = await fs.readFile(localAudioPath);
      const uploadPath = `${taskId}/final_podcast.mp3`;

      logger.info(`📤 Uploading final audio for task ${taskId}: ${audioBuffer.length} bytes`);

      await supabaseService.uploadAudio(audioBuffer, uploadPath);

      // 获取音频文件信息
      const stats = await fs.stat(localAudioPath);
      const duration = await this.getAudioDuration(localAudioPath);

      return {
        path: uploadPath,
        size: stats.size,
        duration: duration
      };
    } catch (error) {
      logger.error(`❌ Failed to upload final audio for task ${taskId}:`, error);
      throw error;
    }
  }

  // 获取音频时长
  async getAudioDuration(audioPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(audioPath, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          const duration = metadata.format.duration || 0;
          resolve(Math.round(duration));
        }
      });
    });
  }

  // 清理临时文件
  async cleanupTempFiles(taskId, downloadedFiles, finalAudioPath) {
    try {
      logger.debug(`🧹 Cleaning up temp files for task ${taskId}`);

      // 删除下载的片段文件
      for (const file of downloadedFiles) {
        try {
          await fs.unlink(file.localPath);
        } catch (error) {
          logger.warn(`Failed to delete temp file ${file.localPath}:`, error);
        }
      }

      // 删除最终音频文件
      try {
        await fs.unlink(finalAudioPath);
      } catch (error) {
        logger.warn(`Failed to delete final audio file ${finalAudioPath}:`, error);
      }

      // 删除任务临时目录
      const taskTempDir = path.join(this.tempDir, taskId);
      try {
        await fs.rmdir(taskTempDir);
      } catch (error) {
        logger.warn(`Failed to delete temp directory ${taskTempDir}:`, error);
      }

      logger.debug(`✅ Cleanup completed for task ${taskId}`);
    } catch (error) {
      logger.error(`❌ Cleanup failed for task ${taskId}:`, error);
    }
  }

  // 获取任务状态
  async getTaskStatus(taskId) {
    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId);
      return {
        status: task.status,
        processing_time_ms: Date.now() - task.startTime
      };
    }

    // 从数据库获取状态
    try {
      const task = await supabaseService.getTask(taskId);
      return {
        status: task.status,
        created_at: task.created_at,
        updated_at: task.updated_at,
        completed_at: task.completed_at,
        failed_at: task.failed_at,
        error_message: task.error_message
      };
    } catch (error) {
      return {
        status: 'unknown',
        error: error.message
      };
    }
  }
}
