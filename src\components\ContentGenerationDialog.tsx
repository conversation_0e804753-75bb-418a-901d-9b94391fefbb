import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { supabase } from '@/integrations/supabase/client';
import { useTranslation } from 'react-i18next';
import {
  Wand2,
  Loader2,
  FileText,
  ExternalLink,
  Sparkles,
  Target,
  Palette,
  MessageSquare
} from 'lucide-react';

interface ContentGenerationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  summaryData: {
    id: string;
    content: string;
    platform: string;
    source_name: string;
    source_urls: string[];
    topic_name?: string;
  };
}

const platformNames = {
  linkedin: 'LinkedIn',
  twitter: 'Twitter/X',
  reddit: 'Reddit',
  xiaohongshu: 'Rednote',
  wechat: 'Wechat'
};

// Style names will be translated using i18n keys

const ContentGenerationDialog: React.FC<ContentGenerationDialogProps> = ({
  open,
  onOpenChange,
  summaryData
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { user } = useAuth();
  const { language } = useLanguage();
  
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [selectedStyle, setSelectedStyle] = useState<string>('engaging');
  const [userInput, setUserInput] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const handlePlatformChange = (platform: string, checked: boolean) => {
    if (checked) {
      setSelectedPlatforms(prev => [...prev, platform]);
    } else {
      setSelectedPlatforms(prev => prev.filter(p => p !== platform));
    }
  };

  const handleGenerate = async () => {
    if (selectedPlatforms.length === 0) {
      toast({
        title: t('contentGeneration.errors.selectPlatform'),
        description: t('contentGeneration.errors.selectPlatformDesc'),
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);

    try {
      // Call content generation queue API
      const { data, error } = await supabase.functions.invoke('content-generation-queue', {
        body: {
          summary_id: summaryData.id,
          target_platforms: selectedPlatforms,
          style: selectedStyle,
          user_input: userInput.trim() || undefined,
          user_id: user?.id,
          user_language: language
        }
      });

      if (error) throw error;

      if (data.success) {
        toast({
          title: t('contentGeneration.success.submitted'),
          description: t('contentGeneration.success.queueInfo', {
            position: data.queue_position,
            waitTime: data.estimated_wait_time
          }),
        });

        // Close dialog
        onOpenChange(false);

        // No longer redirect to generation history page, user can continue browsing current page
      } else {
        throw new Error(data.error || t('contentGeneration.errors.submitFailed'));
      }
    } catch (error: any) {
      console.error('Error generating content:', error);
      toast({
        title: t('contentGeneration.errors.failed'),
        description: error.message || t('contentGeneration.errors.cannotGenerate'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onOpenChange(false);
      // Reset form state
      setSelectedPlatforms([]);
      setSelectedStyle('engaging');
      setUserInput('');
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-primary" />
            {t('contentGeneration.title')}
          </DialogTitle>
          <DialogDescription>
            {t('contentGeneration.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary information preview */}
          <Card>
            <CardContent className="pt-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">{t('contentGeneration.summaryInfo')}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{summaryData.platform}</Badge>
                  <Badge variant="secondary">{summaryData.source_name}</Badge>
                  {summaryData.topic_name && (
                    <Badge variant="outline">{summaryData.topic_name}</Badge>
                  )}
                </div>

                <div className="text-sm text-muted-foreground whitespace-pre-line">
                  {summaryData.content}
                </div>

                {summaryData.source_urls && summaryData.source_urls.length > 0 && (
                  <div className="text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <ExternalLink className="h-3 w-3" />
                      {t('contentGeneration.sourceLinks', { count: summaryData.source_urls.length })}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Platform selection */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-primary" />
              <Label className="text-sm font-medium">{t('contentGeneration.targetPlatforms')}</Label>
            </div>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(platformNames).map(([platform, name]) => (
                <div key={platform} className="flex items-center space-x-2">
                  <Checkbox
                    id={platform}
                    checked={selectedPlatforms.includes(platform)}
                    onCheckedChange={(checked) => handlePlatformChange(platform, checked as boolean)}
                  />
                  <Label htmlFor={platform} className="text-sm cursor-pointer">
                    {name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Style selection */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Palette className="h-4 w-4 text-primary" />
              <Label className="text-sm font-medium">{t('contentGeneration.contentStyle')}</Label>
            </div>
            <RadioGroup value={selectedStyle} onValueChange={setSelectedStyle}>
              <div className="grid grid-cols-1 gap-2">
                {['engaging', 'professional', 'casual', 'creative', 'analytical'].map((style) => (
                  <div key={style} className="flex items-center space-x-2">
                    <RadioGroupItem value={style} id={style} />
                    <Label htmlFor={style} className="text-sm cursor-pointer">
                      {t(`contentGeneration.styles.${style}`)}
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          </div>

          {/* User input */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-primary" />
              <Label htmlFor="user-input" className="text-sm font-medium">
                {t('contentGeneration.userInput')} <span className="text-muted-foreground">({t('contentGeneration.optional')})</span>
              </Label>
            </div>
            <Textarea
              id="user-input"
              placeholder={t('contentGeneration.userInputPlaceholder')}
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <div className="text-xs text-muted-foreground text-right">
              {userInput.length}/500
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleGenerate} disabled={loading || selectedPlatforms.length === 0}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {t('contentGeneration.submitting')}
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                {t('contentGeneration.startGeneration')}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ContentGenerationDialog;
